//@version=5
indicator("Enhanced Volatility Signal Generator", shorttitle="EVSG", overlay=true, max_boxes_count=500, max_lines_count=500)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// INPUT PARAMETERS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === ADAPTIVE MOVING AVERAGES ===
group_ama = "Adaptive Moving Averages"
kama_length = input.int(10, "KAMA Length", minval=1, group=group_ama)
kama_fast_sc = input.float(2.0, "KAMA Fast SC", minval=0.1, group=group_ama)
kama_slow_sc = input.float(30.0, "KAMA Slow SC", minval=1.0, group=group_ama)
jma_length = input.int(14, "JMA Length", minval=1, group=group_ama)
jma_phase = input.float(0.0, "JMA Phase", minval=-100.0, maxval=100.0, group=group_ama)
jma_power = input.float(2.0, "JMA Power", minval=1.0, maxval=10.0, group=group_ama)

// === BOLLINGER BANDS (ADAPTIVE) ===
group_bb = "Adaptive Bollinger Bands"
bb_length = input.int(20, "BB Length", minval=1, group=group_bb)
bb_mult = input.float(2.0, "BB Multiplier", minval=0.1, group=group_bb)
bb_use_kama = input.bool(true, "Use KAMA for BB Basis", group=group_bb)

// === MOMENTUM INDICATORS ===
group_momentum = "Momentum Indicators"
rsi_length = input.int(14, "RSI Length", minval=1, group=group_momentum)
rsi_overbought = input.float(70.0, "RSI Overbought", minval=50.0, maxval=100.0, group=group_momentum)
rsi_oversold = input.float(30.0, "RSI Oversold", minval=0.0, maxval=50.0, group=group_momentum)
stoch_k_length = input.int(14, "Stochastic %K Length", minval=1, group=group_momentum)
stoch_d_length = input.int(3, "Stochastic %D Length", minval=1, group=group_momentum)
williams_length = input.int(14, "Williams %R Length", minval=1, group=group_momentum)

// === VOLUME ANALYSIS ===
group_volume = "Volume Analysis"
obv_ma_length = input.int(20, "OBV MA Length", minval=1, group=group_volume)
adl_ma_length = input.int(20, "ADL MA Length", minval=1, group=group_volume)
volume_threshold = input.float(1.5, "High Volume Threshold (x Average)", minval=0.1, group=group_volume)
volume_ma_length = input.int(20, "Volume MA Length", minval=1, group=group_volume)

// === MARKET STRUCTURE ===
group_structure = "Market Structure"
pivot_left = input.int(5, "Pivot Left Bars", minval=1, group=group_structure)
pivot_right = input.int(5, "Pivot Right Bars", minval=1, group=group_structure)
max_pivots = input.int(10, "Max Pivot Points", minval=1, maxval=50, group=group_structure)

// === MULTI-TIMEFRAME ===
group_mtf = "Multi-Timeframe Analysis"
htf_timeframe = input.timeframe("1H", "Higher Timeframe", group=group_mtf)
mtf_enabled = input.bool(true, "Enable MTF Confirmation", group=group_mtf)

// === RISK MANAGEMENT ===
group_risk = "Risk Management"
atr_length = input.int(14, "ATR Length", minval=1, group=group_risk)
atr_stop_mult = input.float(2.0, "ATR Stop Loss Multiplier", minval=0.1, group=group_risk)
fib_tp1_level = input.float(1.272, "Fibonacci TP1 Level", minval=1.0, group=group_risk)
fib_tp2_level = input.float(1.618, "Fibonacci TP2 Level", minval=1.0, group=group_risk)

// === MARKET REGIME ===
group_regime = "Market Regime Detection"
regime_length = input.int(20, "Regime Detection Length", minval=5, group=group_regime)
trend_threshold = input.float(0.5, "Trend Strength Threshold", minval=0.1, maxval=1.0, group=group_regime)

// === SIGNAL SETTINGS ===
group_signals = "Signal Settings"
min_signal_strength = input.float(0.6, "Minimum Signal Strength", minval=0.1, maxval=1.0, group=group_signals)
enable_alerts = input.bool(true, "Enable Alerts", group=group_signals)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// HELPER FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Kaufman's Adaptive Moving Average (KAMA) - Enhanced Implementation
kama(src, length, fast_sc, slow_sc) =>
    var float kama_val = na
    if bar_index == 0
        kama_val := src
    else
        // Calculate Efficiency Ratio (ER)
        change = math.abs(src - src[length])
        volatility = math.sum(math.abs(src - src[1]), length)
        er = volatility != 0 ? change / volatility : 0

        // Calculate Smoothing Constant (SC)
        fastest_sc = 2.0 / (fast_sc + 1)
        slowest_sc = 2.0 / (slow_sc + 1)
        sc = math.pow((er * (fastest_sc - slowest_sc) + slowest_sc), 2)

        // Apply KAMA formula
        kama_val := kama_val + sc * (src - kama_val)
    kama_val

// Enhanced Jurik Moving Average (JMA) Implementation
jma(src, length, phase, power) =>
    // JMA variables
    var float e0 = 0.0, var float e1 = 0.0, var float e2 = 0.0
    var float jma_val = 0.0

    // Phase and power adjustments
    phaseRatio = phase < -100 ? 0.5 : phase > 100 ? 2.5 : phase / 100 + 1.5
    beta = 0.45 * (length - 1) / (0.45 * (length - 1) + 2)
    alpha = math.pow(beta, power)

    if bar_index == 0
        e0 := src
        e1 := src
        e2 := src
        jma_val := src
    else
        // Three-stage adaptive smoothing
        e0 := (1 - alpha) * src + alpha * e0
        e1 := (src - e0) * (1 - beta) + beta * e1
        e2 := (e0 + phaseRatio * e1 - jma_val) * math.pow((1 - alpha), 2) + math.pow(alpha, 2) * e2
        jma_val := e2 + jma_val

    jma_val

// Enhanced Divergence Detection Function
detect_divergence(price_series, indicator_series, lookback) =>
    // Find recent highs and lows with confirmation
    ph = ta.pivothigh(price_series, lookback, lookback)
    pl = ta.pivotlow(price_series, lookback, lookback)
    ih = ta.pivothigh(indicator_series, lookback, lookback)
    il = ta.pivotlow(indicator_series, lookback, lookback)

    var float last_ph = na, var float last_pl = na
    var float last_ih = na, var float last_il = na
    var int last_ph_bar = na, var int last_pl_bar = na
    var int last_ih_bar = na, var int last_il_bar = na

    // Update pivot points with bar tracking
    if not na(ph)
        last_ph := ph
        last_ph_bar := bar_index
    if not na(pl)
        last_pl := pl
        last_pl_bar := bar_index
    if not na(ih)
        last_ih := ih
        last_ih_bar := bar_index
    if not na(il)
        last_il := il
        last_il_bar := bar_index

    // Enhanced divergence detection with time constraints
    bullish_div = false
    bearish_div = false

    // Bullish divergence: price lower low, indicator higher low
    if not na(last_pl) and not na(last_il) and bar_index - last_pl_bar <= lookback * 3
        if pl < last_pl and il > last_il and math.abs(last_pl_bar - last_il_bar) <= lookback
            bullish_div := true

    // Bearish divergence: price higher high, indicator lower high
    if not na(last_ph) and not na(last_ih) and bar_index - last_ph_bar <= lookback * 3
        if ph > last_ph and ih < last_ih and math.abs(last_ph_bar - last_ih_bar) <= lookback
            bearish_div := true

    [bullish_div, bearish_div]

// Volume Spread Analysis (VSA) Functions
vsa_analysis() =>
    // Calculate spread (range) and volume characteristics
    spread = high - low
    avg_spread = ta.sma(spread, 20)
    avg_volume = ta.sma(volume, 20)

    // Relative measurements
    spread_ratio = spread / avg_spread
    volume_ratio = volume / avg_volume
    close_position = (close - low) / spread  // 0 = low, 1 = high

    // VSA Pattern Detection
    is_wide_spread = spread_ratio > 1.5
    is_narrow_spread = spread_ratio < 0.7
    is_high_volume = volume_ratio > 1.5
    is_low_volume = volume_ratio < 0.7
    is_up_bar = close > open
    is_down_bar = close < open

    // VSA Patterns
    no_demand = is_up_bar and is_low_volume and is_narrow_spread
    no_supply = is_down_bar and is_low_volume and is_narrow_spread
    accumulation = is_down_bar and is_high_volume and close_position > 0.5
    distribution = is_up_bar and is_high_volume and close_position < 0.5
    climactic_volume = is_high_volume and is_wide_spread
    stopping_volume = is_high_volume and close_position > 0.3 and close_position < 0.7

    [no_demand, no_supply, accumulation, distribution, climactic_volume, stopping_volume]

// Dynamic Support and Resistance Detection
dynamic_levels() =>
    // Pivot point detection
    pivot_high = ta.pivothigh(high, pivot_left, pivot_right)
    pivot_low = ta.pivotlow(low, pivot_left, pivot_right)

    // Arrays to store pivot levels
    var array<float> resistance_levels = array.new<float>()
    var array<float> support_levels = array.new<float>()
    var array<int> resistance_bars = array.new<int>()
    var array<int> support_bars = array.new<int>()

    // Add new pivot points
    if not na(pivot_high)
        if array.size(resistance_levels) >= max_pivots
            array.shift(resistance_levels)
            array.shift(resistance_bars)
        array.push(resistance_levels, pivot_high)
        array.push(resistance_bars, bar_index)

    if not na(pivot_low)
        if array.size(support_levels) >= max_pivots
            array.shift(support_levels)
            array.shift(support_bars)
        array.push(support_levels, pivot_low)
        array.push(support_bars, bar_index)

    // Find nearest levels
    nearest_resistance = na(float)
    nearest_support = na(float)

    if array.size(resistance_levels) > 0
        for i = 0 to array.size(resistance_levels) - 1
            level = array.get(resistance_levels, i)
            if level > close and (na(nearest_resistance) or level < nearest_resistance)
                nearest_resistance := level

    if array.size(support_levels) > 0
        for i = 0 to array.size(support_levels) - 1
            level = array.get(support_levels, i)
            if level < close and (na(nearest_support) or level > nearest_support)
                nearest_support := level

    [nearest_resistance, nearest_support]

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// CORE CALCULATIONS
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === ADAPTIVE MOVING AVERAGES ===
kama_close = kama(close, kama_length, kama_fast_sc, kama_slow_sc)
jma_close = jma(close, jma_length, jma_phase, jma_power)

// === ADAPTIVE BOLLINGER BANDS ===
bb_basis = bb_use_kama ? kama_close : ta.sma(close, bb_length)
bb_dev = ta.stdev(close, bb_length) * bb_mult
bb_upper = bb_basis + bb_dev
bb_lower = bb_basis - bb_dev
bb_width = (bb_upper - bb_lower) / bb_basis * 100

// === MOMENTUM INDICATORS ===
rsi = ta.rsi(close, rsi_length)
stoch_k = ta.stoch(close, high, low, stoch_k_length)
stoch_d = ta.sma(stoch_k, stoch_d_length)
williams_r = ta.wpr(williams_length)

// === VOLUME INDICATORS ===
obv = ta.obv
adl = ta.accdist
volume_ma = ta.sma(volume, volume_ma_length)
high_volume = volume > volume_ma * volume_threshold
obv_ma = ta.sma(obv, obv_ma_length)
adl_ma = ta.sma(adl, adl_ma_length)

// === ATR FOR VOLATILITY ===
atr = ta.atr(atr_length)

// === VSA ANALYSIS ===
[no_demand, no_supply, accumulation, distribution, climactic_volume, stopping_volume] = vsa_analysis()

// === DYNAMIC SUPPORT/RESISTANCE ===
[nearest_resistance, nearest_support] = dynamic_levels()

// === MULTI-TIMEFRAME ANALYSIS ===
htf_close = request.security(syminfo.tickerid, htf_timeframe, close, lookahead=barmerge.lookahead_off)
htf_kama = request.security(syminfo.tickerid, htf_timeframe, kama(close, kama_length, kama_fast_sc, kama_slow_sc), lookahead=barmerge.lookahead_off)
htf_trend_up = htf_close > htf_kama
htf_trend_down = htf_close < htf_kama

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// DIVERGENCE DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

[rsi_bull_div, rsi_bear_div] = detect_divergence(close, rsi, 5)
[stoch_bull_div, stoch_bear_div] = detect_divergence(close, stoch_k, 5)
[williams_bull_div, williams_bear_div] = detect_divergence(close, williams_r, 5)
[obv_bull_div, obv_bear_div] = detect_divergence(close, obv, 10)
[adl_bull_div, adl_bear_div] = detect_divergence(close, adl, 10)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// MARKET REGIME DETECTION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Trend strength based on moving average slope and ATR
ma_slope = (kama_close - kama_close[regime_length]) / regime_length
normalized_slope = ma_slope / atr
trend_strength = math.abs(normalized_slope)

// Market regime classification
is_strong_trend = trend_strength > trend_threshold
is_uptrend = ma_slope > 0
is_downtrend = ma_slope < 0
is_ranging = not is_strong_trend

// Volatility regime
volatility_percentile = ta.percentrank(atr, regime_length * 2)
is_high_volatility = volatility_percentile > 70
is_low_volatility = volatility_percentile < 30

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// ENHANCED SIGNAL GENERATION SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === MOMENTUM SIGNAL COMPONENTS ===
// Divergence signals (highest weight)
momentum_divergence_bull = rsi_bull_div or stoch_bull_div or williams_bull_div
momentum_divergence_bear = rsi_bear_div or stoch_bear_div or williams_bear_div

// Oversold/Overbought with divergence confirmation
momentum_oversold = rsi < rsi_oversold and stoch_k < 20 and williams_r < -80
momentum_overbought = rsi > rsi_overbought and stoch_k > 80 and williams_r > -20

// === VOLUME SIGNAL COMPONENTS ===
// Volume divergence signals
volume_divergence_bull = obv_bull_div or adl_bull_div
volume_divergence_bear = obv_bear_div or adl_bear_div

// VSA pattern signals
vsa_bullish = accumulation or no_supply or (stopping_volume and close > open)
vsa_bearish = distribution or no_demand or (stopping_volume and close < open)

// Volume confirmation
volume_confirmation = high_volume and climactic_volume

// === PRICE ACTION COMPONENTS ===
// Bollinger Band position and expansion
bb_expansion = bb_width > bb_width[1] and bb_width > ta.sma(bb_width, 10)
bb_squeeze = bb_width < ta.sma(bb_width, 20) * 0.8

// Price position relative to adaptive bands
price_near_lower = close <= bb_lower * 1.02  // Within 2% of lower band
price_near_upper = close >= bb_upper * 0.98  // Within 2% of upper band
price_above_kama = close > kama_close
price_below_kama = close < kama_close

// === MARKET STRUCTURE COMPONENTS ===
// Support/Resistance proximity
near_support = not na(nearest_support) and close <= nearest_support * 1.01
near_resistance = not na(nearest_resistance) and close >= nearest_resistance * 0.99
support_break = not na(nearest_support) and close < nearest_support * 0.99
resistance_break = not na(nearest_resistance) and close > nearest_resistance * 1.01

// === MULTI-TIMEFRAME CONFIRMATION ===
mtf_bullish_confirm = not mtf_enabled or htf_trend_up
mtf_bearish_confirm = not mtf_enabled or htf_trend_down

// === COMPREHENSIVE SIGNAL CALCULATION ===
// Bullish signal strength (weighted components)
bullish_momentum_score = (momentum_divergence_bull ? 0.4 : 0) + (momentum_oversold ? 0.2 : 0)
bullish_volume_score = (volume_divergence_bull ? 0.3 : 0) + (vsa_bullish ? 0.2 : 0) + (volume_confirmation ? 0.1 : 0)
bullish_price_score = (price_near_lower ? 0.2 : 0) + (bb_expansion ? 0.15 : 0) + (price_above_kama ? 0.1 : 0)
bullish_structure_score = (near_support ? 0.2 : 0) + (support_break and close > nearest_support ? 0.1 : 0)
bullish_mtf_score = mtf_bullish_confirm ? 0.2 : 0

// Bearish signal strength (weighted components)
bearish_momentum_score = (momentum_divergence_bear ? 0.4 : 0) + (momentum_overbought ? 0.2 : 0)
bearish_volume_score = (volume_divergence_bear ? 0.3 : 0) + (vsa_bearish ? 0.2 : 0) + (volume_confirmation ? 0.1 : 0)
bearish_price_score = (price_near_upper ? 0.2 : 0) + (bb_expansion ? 0.15 : 0) + (price_below_kama ? 0.1 : 0)
bearish_structure_score = (near_resistance ? 0.2 : 0) + (resistance_break and close < nearest_resistance ? 0.1 : 0)
bearish_mtf_score = mtf_bearish_confirm ? 0.2 : 0

// Total signal strength (0-1 scale)
bullish_strength = bullish_momentum_score + bullish_volume_score + bullish_price_score + bullish_structure_score + bullish_mtf_score
bearish_strength = bearish_momentum_score + bearish_volume_score + bearish_price_score + bearish_structure_score + bearish_mtf_score

// === REGIME-ADAPTIVE SIGNAL FILTERING ===
// Adjust signals based on market regime
regime_adjusted_bull_strength = bullish_strength * (is_strong_trend and is_uptrend ? 1.2 : is_ranging ? 0.8 : 0.6)
regime_adjusted_bear_strength = bearish_strength * (is_strong_trend and is_downtrend ? 1.2 : is_ranging ? 0.8 : 0.6)

// Final signal generation with regime consideration
bullish_signal = regime_adjusted_bull_strength >= min_signal_strength and not bearish_signal[1]
bearish_signal = regime_adjusted_bear_strength >= min_signal_strength and not bullish_signal[1]

// === RISK MANAGEMENT CALCULATIONS ===
// ATR-based stop loss
atr_stop_distance = atr * atr_stop_mult
bullish_stop_loss = bullish_signal ? close - atr_stop_distance : na
bearish_stop_loss = bearish_signal ? close + atr_stop_distance : na

// Fibonacci-based take profit (simplified)
price_swing = ta.highest(high, 20) - ta.lowest(low, 20)
fib_tp1_distance = price_swing * (fib_tp1_level - 1.0)
fib_tp2_distance = price_swing * (fib_tp2_level - 1.0)

bullish_tp1 = bullish_signal ? close + fib_tp1_distance : na
bullish_tp2 = bullish_signal ? close + fib_tp2_distance : na
bearish_tp1 = bearish_signal ? close - fib_tp1_distance : na
bearish_tp2 = bearish_signal ? close - fib_tp2_distance : na

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// ENHANCED PLOTTING AND VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// === ADAPTIVE MOVING AVERAGES ===
kama_color = is_uptrend ? color.blue : is_downtrend ? color.red : color.gray
jma_color = close > jma_close ? color.purple : color.orange
plot(kama_close, "KAMA", color=kama_color, linewidth=2)
plot(jma_close, "JMA", color=jma_color, linewidth=1)

// === ADAPTIVE BOLLINGER BANDS ===
bb_color = bb_expansion ? color.yellow : color.gray
p1 = plot(bb_upper, "BB Upper", color=bb_color, linewidth=1)
p2 = plot(bb_lower, "BB Lower", color=bb_color, linewidth=1)
plot(bb_basis, "BB Basis", color=bb_color, linewidth=2)
fill(p1, p2, color=color.new(bb_color, 95))

// === DYNAMIC SUPPORT/RESISTANCE LEVELS ===
var line resistance_line = na
var line support_line = na

if not na(nearest_resistance)
    if not na(resistance_line)
        line.delete(resistance_line)
    resistance_line := line.new(bar_index - 10, nearest_resistance, bar_index + 10, nearest_resistance,
                                color=color.red, style=line.style_dashed, width=1)

if not na(nearest_support)
    if not na(support_line)
        line.delete(support_line)
    support_line := line.new(bar_index - 10, nearest_support, bar_index + 10, nearest_support,
                             color=color.green, style=line.style_dashed, width=1)

// === MAIN TRADING SIGNALS ===
// Enhanced signal shapes with strength indication
signal_size = bullish_strength > 0.8 or bearish_strength > 0.8 ? size.large : size.normal
plotshape(bullish_signal, "Bullish Signal", shape.triangleup, location.belowbar,
          color=color.new(color.lime, 0), size=signal_size, text="BUY")
plotshape(bearish_signal, "Bearish Signal", shape.triangledown, location.abovebar,
          color=color.new(color.red, 0), size=signal_size, text="SELL")

// === DIVERGENCE INDICATORS ===
// Momentum divergences
plotchar(rsi_bull_div, "RSI Bull Div", "R↑", location.belowbar, color=color.lime, size=size.tiny)
plotchar(rsi_bear_div, "RSI Bear Div", "R↓", location.abovebar, color=color.red, size=size.tiny)
plotchar(stoch_bull_div, "Stoch Bull Div", "S↑", location.belowbar, color=color.aqua, size=size.tiny)
plotchar(stoch_bear_div, "Stoch Bear Div", "S↓", location.abovebar, color=color.orange, size=size.tiny)

// Volume divergences
plotchar(obv_bull_div, "OBV Bull Div", "O↑", location.belowbar, color=color.green, size=size.tiny)
plotchar(obv_bear_div, "OBV Bear Div", "O↓", location.abovebar, color=color.maroon, size=size.tiny)

// === VSA PATTERN INDICATORS ===
plotchar(accumulation, "Accumulation", "A", location.belowbar, color=color.blue, size=size.small)
plotchar(distribution, "Distribution", "D", location.abovebar, color=color.purple, size=size.small)
plotchar(no_supply, "No Supply", "NS", location.belowbar, color=color.teal, size=size.tiny)
plotchar(no_demand, "No Demand", "ND", location.abovebar, color=color.yellow, size=size.tiny)
plotchar(climactic_volume, "Climactic Volume", "CV", location.absolute, color=color.white, size=size.small)

// === RISK MANAGEMENT LEVELS ===
// Stop loss levels
plotshape(bullish_signal and not na(bullish_stop_loss), "Bull Stop", shape.cross, location.absolute,
          color=color.red, size=size.tiny, text="SL")
plotshape(bearish_signal and not na(bearish_stop_loss), "Bear Stop", shape.cross, location.absolute,
          color=color.red, size=size.tiny, text="SL")

// Take profit levels
plotshape(bullish_signal and not na(bullish_tp1), "Bull TP1", shape.diamond, location.absolute,
          color=color.green, size=size.tiny, text="TP1")
plotshape(bearish_signal and not na(bearish_tp1), "Bear TP1", shape.diamond, location.absolute,
          color=color.green, size=size.tiny, text="TP1")

// === MARKET REGIME BACKGROUND ===
bgcolor(is_strong_trend and is_uptrend ? color.new(color.green, 95) :
        is_strong_trend and is_downtrend ? color.new(color.red, 95) :
        is_ranging ? color.new(color.yellow, 98) : na, title="Market Regime")

// === INFORMATION TABLE ===
if barstate.islast
    var table info_table = table.new(position.top_right, 3, 8, bgcolor=color.white, border_width=1)

    table.cell(info_table, 0, 0, "Metric", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Value", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 2, 0, "Status", text_color=color.black, bgcolor=color.gray)

    table.cell(info_table, 0, 1, "Bull Strength", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(bullish_strength, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 1, bullish_strength >= min_signal_strength ? "✓" : "✗",
               text_color=bullish_strength >= min_signal_strength ? color.green : color.red)

    table.cell(info_table, 0, 2, "Bear Strength", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(bearish_strength, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 2, bearish_strength >= min_signal_strength ? "✓" : "✗",
               text_color=bearish_strength >= min_signal_strength ? color.green : color.red)

    table.cell(info_table, 0, 3, "Market Regime", text_color=color.black)
    regime_text = is_strong_trend ? (is_uptrend ? "Strong Up" : "Strong Down") : "Ranging"
    table.cell(info_table, 1, 3, regime_text, text_color=color.black)
    table.cell(info_table, 2, 3, is_strong_trend ? "✓" : "⚠",
               text_color=is_strong_trend ? color.green : color.orange)

    table.cell(info_table, 0, 4, "Volatility", text_color=color.black)
    vol_text = is_high_volatility ? "High" : is_low_volatility ? "Low" : "Normal"
    table.cell(info_table, 1, 4, vol_text, text_color=color.black)
    table.cell(info_table, 2, 4, is_high_volatility ? "⚠" : "✓",
               text_color=is_high_volatility ? color.orange : color.green)

    table.cell(info_table, 0, 5, "HTF Trend", text_color=color.black)
    htf_text = htf_trend_up ? "Up" : htf_trend_down ? "Down" : "Neutral"
    table.cell(info_table, 1, 5, htf_text, text_color=color.black)
    table.cell(info_table, 2, 5, htf_trend_up or htf_trend_down ? "✓" : "⚠",
               text_color=htf_trend_up or htf_trend_down ? color.green : color.orange)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// COMPREHENSIVE ALERT SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

if enable_alerts
    // === MAIN TRADING SIGNALS ===
    if bullish_signal
        alert_msg = "🟢 EVSG BULLISH SIGNAL\n" +
                   "Strength: " + str.tostring(bullish_strength, "#.##") + "\n" +
                   "Price: " + str.tostring(close, "#.##") + "\n" +
                   "Stop Loss: " + str.tostring(bullish_stop_loss, "#.##") + "\n" +
                   "Take Profit 1: " + str.tostring(bullish_tp1, "#.##") + "\n" +
                   "Take Profit 2: " + str.tostring(bullish_tp2, "#.##") + "\n" +
                   "Market Regime: " + (is_strong_trend ? "Strong Trend" : "Ranging") + "\n" +
                   "HTF Trend: " + (htf_trend_up ? "Bullish" : "Neutral/Bearish")
        alert(alert_msg, alert.freq_once_per_bar)

    if bearish_signal
        alert_msg = "🔴 EVSG BEARISH SIGNAL\n" +
                   "Strength: " + str.tostring(bearish_strength, "#.##") + "\n" +
                   "Price: " + str.tostring(close, "#.##") + "\n" +
                   "Stop Loss: " + str.tostring(bearish_stop_loss, "#.##") + "\n" +
                   "Take Profit 1: " + str.tostring(bearish_tp1, "#.##") + "\n" +
                   "Take Profit 2: " + str.tostring(bearish_tp2, "#.##") + "\n" +
                   "Market Regime: " + (is_strong_trend ? "Strong Trend" : "Ranging") + "\n" +
                   "HTF Trend: " + (htf_trend_down ? "Bearish" : "Neutral/Bullish")
        alert(alert_msg, alert.freq_once_per_bar)

    // === DIVERGENCE ALERTS ===
    if momentum_divergence_bull
        alert("📈 BULLISH MOMENTUM DIVERGENCE DETECTED - Potential reversal signal", alert.freq_once_per_bar)

    if momentum_divergence_bear
        alert("📉 BEARISH MOMENTUM DIVERGENCE DETECTED - Potential reversal signal", alert.freq_once_per_bar)

    if volume_divergence_bull
        alert("📊 BULLISH VOLUME DIVERGENCE DETECTED - Volume not confirming price decline", alert.freq_once_per_bar)

    if volume_divergence_bear
        alert("📊 BEARISH VOLUME DIVERGENCE DETECTED - Volume not confirming price rise", alert.freq_once_per_bar)

    // === VSA PATTERN ALERTS ===
    if accumulation
        alert("🔵 VSA ACCUMULATION PATTERN - Smart money buying detected", alert.freq_once_per_bar)

    if distribution
        alert("🟣 VSA DISTRIBUTION PATTERN - Smart money selling detected", alert.freq_once_per_bar)

    if climactic_volume
        alert("⚡ CLIMACTIC VOLUME - Potential trend exhaustion", alert.freq_once_per_bar)

    // === MARKET STRUCTURE ALERTS ===
    if support_break
        alert("⬇️ SUPPORT LEVEL BROKEN - Price: " + str.tostring(close, "#.##") + " | Support: " + str.tostring(nearest_support, "#.##"), alert.freq_once_per_bar)

    if resistance_break
        alert("⬆️ RESISTANCE LEVEL BROKEN - Price: " + str.tostring(close, "#.##") + " | Resistance: " + str.tostring(nearest_resistance, "#.##"), alert.freq_once_per_bar)

// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════
// PERFORMANCE METRICS AND SUMMARY
// ═══════════════════════════════════════════════════════════════════════════════════════════════════════════════════════

// Signal quality metrics for optimization
var int total_signals = 0
var int strong_signals = 0

if bullish_signal or bearish_signal
    total_signals := total_signals + 1
    if (bullish_signal and bullish_strength > 0.8) or (bearish_signal and bearish_strength > 0.8)
        strong_signals := strong_signals + 1

// Calculate signal quality ratio
signal_quality_ratio = total_signals > 0 ? strong_signals / total_signals : 0

// Export key values for external analysis
plot(bullish_strength, "Bullish Strength", color=color.new(color.green, 70), display=display.data_window)
plot(bearish_strength, "Bearish Strength", color=color.new(color.red, 70), display=display.data_window)
plot(bb_width, "BB Width %", color=color.new(color.blue, 70), display=display.data_window)
plot(trend_strength, "Trend Strength", color=color.new(color.purple, 70), display=display.data_window)
plot(signal_quality_ratio, "Signal Quality Ratio", color=color.new(color.orange, 70), display=display.data_window)
